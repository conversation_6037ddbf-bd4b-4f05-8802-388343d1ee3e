/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量定义 */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease-in-out;
}

/* 基础样式 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: #eff6ff;
}

/* 主要内容区域 */
.main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

/* 区域样式 */
.input-section,
.result-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    overflow: hidden;
}

.section-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header h2 i {
    color: var(--primary-color);
}

.section-header p {
    color: var(--text-secondary);
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

/* 输入容器样式 */
.input-container {
    padding: 2rem;
}

.input-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.toolbar-btn {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toolbar-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.word-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 文本域样式 */
#requirementInput {
    width: 100%;
    min-height: 300px;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.6;
    resize: vertical;
    transition: var(--transition);
    background: var(--surface-color);
}

#requirementInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

#requirementInput::placeholder {
    color: var(--text-secondary);
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #475569;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.input-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* 结果容器样式 */
.result-container {
    padding: 2rem;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 3rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 结果内容样式 */
.result-content {
    display: none;
}

.result-item {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.result-item h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.result-item p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.6;
}

/* 页脚样式 */
.footer {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 提示消息样式 */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 1rem 1.5rem;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 1000;
    max-width: 400px;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast-icon {
    font-size: 1.25rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--error-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

/* 分析结果详细样式 */
.result-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.result-summary h3 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.summary-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.summary-value {
    font-weight: 600;
    font-size: 1rem;
}

.summary-value.success {
    color: #10b981;
}

.result-details h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.result-details h3 i {
    color: var(--primary-color);
}

.analysis-content {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.result-section {
    margin-bottom: 1.5rem;
}

.result-section h4 {
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.requirement-list,
.suggestion-list {
    list-style: none;
    padding: 0;
}

.requirement-list li,
.suggestion-list li {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 2rem;
}

.requirement-list li::before {
    content: '✓';
    position: absolute;
    left: 0.75rem;
    color: var(--success-color);
    font-weight: bold;
}

.suggestion-list li::before {
    content: '💡';
    position: absolute;
    left: 0.75rem;
}

.result-text,
.analysis-text {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
}

.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .main {
        padding: 1rem;
    }

    .input-container,
    .result-container {
        padding: 1rem;
    }

    .section-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .input-actions {
        flex-direction: column;
    }

    .input-toolbar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .toast {
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .result-summary {
        padding: 1rem;
    }

    .analysis-content {
        padding: 1rem;
    }
}
