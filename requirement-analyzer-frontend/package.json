{"name": "requirement-analyzer-frontend", "version": "1.0.0", "description": "需求文档分析系统前端界面", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "serve": "node server.js", "build": "echo '构建完成：当前项目为纯静态文件，无需构建步骤'", "build:prod": "echo '生产环境构建：复制静态文件到dist目录' && mkdir -p dist && cp *.html *.css *.js dist/", "clean": "rm -rf dist node_modules", "test": "echo '暂无测试用例'"}, "keywords": ["requirement", "analysis", "frontend", "document", "analyzer"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "."}, "dependencies": {}, "devDependencies": {}}