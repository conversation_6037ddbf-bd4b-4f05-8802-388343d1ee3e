# 需求文档分析系统 - 前端界面

一个现代化的需求文档分析系统前端界面，采用经典的设计风格，支持用户输入需求文档并与后端API进行交互。

## 🌟 功能特性

- **现代化UI设计**: 采用经典的蓝色主题，简洁美观
- **实时字数统计**: 自动统计输入文档的字符数
- **自动保存草稿**: 输入内容自动保存到本地存储
- **智能分析**: 与后端API交互，获取需求分析结果
- **结果导出**: 支持将分析结果导出为文本文件
- **响应式设计**: 适配桌面和移动设备
- **快捷键支持**: 支持Ctrl+Enter分析，Ctrl+S保存等
- **状态提示**: 友好的操作反馈和错误提示

## 🚀 快速开始

### 前置要求

- Node.js 12.0.0 或更高版本
- 后端API服务运行在 `localhost:8000`

### 安装和运行

1. **进入项目目录**
   ```bash
   cd requirement-analyzer-frontend
   ```

2. **启动前端服务器**
   ```bash
   npm start
   # 或者
   node server.js
   ```

3. **访问应用**
   打开浏览器访问: http://localhost:5000

### 项目结构

```
requirement-analyzer-frontend/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
├── server.js           # Node.js静态服务器
├── package.json        # 项目配置
└── README.md          # 说明文档
```

## 🎨 界面设计

### 设计特点

- **经典蓝色主题**: 使用专业的蓝色调色板
- **卡片式布局**: 清晰的内容分区
- **现代字体**: 使用Inter字体提升阅读体验
- **图标支持**: 集成Font Awesome图标库
- **渐变效果**: 精美的渐变背景和按钮效果

### 主要组件

1. **头部导航**: 包含Logo和导航链接
2. **输入区域**: 需求文档输入框和工具栏
3. **分析结果**: 结构化的分析结果展示
4. **操作按钮**: 清空、粘贴、保存、分析、导出等功能
5. **状态提示**: Toast消息提示系统

## 🔧 API接口

前端与后端的交互通过以下API接口：

### 分析接口

**POST** `http://localhost:8000/api/analyze`

**请求体:**
```json
{
  "content": "需求文档内容",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**响应格式:**
```json
{
  "id": "分析ID",
  "analysis": "分析结果",
  "requirements": ["需求1", "需求2"],
  "suggestions": ["建议1", "建议2"]
}
```

## 🎯 使用说明

### 基本操作

1. **输入需求文档**: 在文本框中输入或粘贴需求文档内容
2. **开始分析**: 点击"开始分析"按钮或使用Ctrl+Enter快捷键
3. **查看结果**: 分析完成后在下方查看结构化的分析结果
4. **导出结果**: 点击"导出结果"按钮下载分析报告

### 快捷键

- `Ctrl + Enter`: 开始分析
- `Ctrl + S`: 保存草稿

### 功能按钮

- **清空**: 清除所有输入内容
- **粘贴**: 从剪贴板粘贴内容
- **保存草稿**: 手动保存当前内容
- **开始分析**: 发送内容到后端进行分析
- **导出结果**: 下载分析结果文件

## 🔧 技术栈

- **HTML5**: 语义化标记
- **CSS3**: 现代样式和动画
- **JavaScript (ES6+)**: 原生JavaScript，无框架依赖
- **Node.js**: 静态文件服务器
- **Font Awesome**: 图标库
- **Google Fonts**: Inter字体

## 🎨 样式特性

### CSS变量系统

使用CSS自定义属性管理主题色彩：

```css
:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
}
```

### 响应式设计

- 桌面优先的设计方法
- 移动设备适配
- 弹性布局和网格系统

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```
   Error: listen EADDRINUSE :::5000
   ```
   解决方案: 修改`server.js`中的PORT变量或关闭占用端口的程序

2. **后端连接失败**
   ```
   分析失败，请检查网络连接或稍后重试
   ```
   解决方案: 确保后端服务在localhost:8000正常运行

3. **粘贴功能不工作**
   - 确保浏览器支持Clipboard API
   - 在HTTPS环境下使用或允许不安全的本地连接

### 调试模式

打开浏览器开发者工具(F12)查看：
- Console面板: JavaScript错误和日志
- Network面板: API请求状态
- Elements面板: DOM结构和样式

## 📝 开发说明

### 自定义样式

修改`styles.css`中的CSS变量来自定义主题：

```css
:root {
  --primary-color: #your-color;
  --border-radius: 8px;
  --transition: all 0.2s ease-in-out;
}
```

### 扩展功能

在`script.js`中添加新功能：

```javascript
// 添加新的事件处理器
function handleNewFeature() {
  // 功能实现
}

// 注册事件监听器
document.getElementById('newButton').addEventListener('click', handleNewFeature);
```

## 📄 许可证

MIT License - 详见LICENSE文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
