// 全局变量
const API_BASE_URL = 'http://localhost:8000';
let currentAnalysisId = null;

// DOM 元素
const requirementInput = document.getElementById('requirementInput');
const wordCount = document.getElementById('wordCount');
const analyzeBtn = document.getElementById('analyzeBtn');
const clearBtn = document.getElementById('clearBtn');
const pasteBtn = document.getElementById('pasteBtn');
const saveBtn = document.getElementById('saveBtn');
const exportBtn = document.getElementById('exportBtn');
const resultSection = document.getElementById('resultSection');
const loadingSpinner = document.getElementById('loadingSpinner');
const resultContent = document.getElementById('resultContent');
const toast = document.getElementById('toast');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadDraftFromStorage();
    updateWordCount();
});

// 事件监听器初始化
function initializeEventListeners() {
    // 文本输入事件
    requirementInput.addEventListener('input', function() {
        updateWordCount();
        saveDraftToStorage();
    });

    // 按钮点击事件
    analyzeBtn.addEventListener('click', handleAnalyze);
    clearBtn.addEventListener('click', handleClear);
    pasteBtn.addEventListener('click', handlePaste);
    saveBtn.addEventListener('click', handleSave);
    exportBtn.addEventListener('click', handleExport);

    // 键盘快捷键
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'Enter':
                    e.preventDefault();
                    handleAnalyze();
                    break;
                case 's':
                    e.preventDefault();
                    handleSave();
                    break;
            }
        }
    });
}

// 更新字数统计
function updateWordCount() {
    const text = requirementInput.value.trim();
    const count = text.length;
    wordCount.textContent = count;
    
    // 更新分析按钮状态
    analyzeBtn.disabled = count === 0;
    if (count === 0) {
        analyzeBtn.classList.add('disabled');
    } else {
        analyzeBtn.classList.remove('disabled');
    }
}

// 处理分析请求
async function handleAnalyze() {
    const content = requirementInput.value.trim();
    
    if (!content) {
        showToast('请输入需求文档内容', 'warning');
        return;
    }

    // 显示结果区域和加载状态
    resultSection.style.display = 'block';
    loadingSpinner.style.display = 'block';
    resultContent.style.display = 'none';
    
    // 滚动到结果区域
    resultSection.scrollIntoView({ behavior: 'smooth' });
    
    // 禁用分析按钮
    analyzeBtn.disabled = true;
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';

    try {
        const response = await fetch(`${API_BASE_URL}/api/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                content: content,
                timestamp: new Date().toISOString()
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        displayAnalysisResult(result);
        showToast('分析完成！', 'success');
        
    } catch (error) {
        console.error('分析请求失败:', error);
        showToast('分析失败，请检查网络连接或稍后重试', 'error');
        resultSection.style.display = 'none';
    } finally {
        // 恢复分析按钮
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-cogs"></i> 开始分析';
    }
}

// 显示分析结果
function displayAnalysisResult(result) {
    loadingSpinner.style.display = 'none';
    resultContent.style.display = 'block';
    
    // 清空之前的结果
    resultContent.innerHTML = '';
    
    // 创建结果展示
    const resultHTML = `
        <div class="result-summary">
            <h3><i class="fas fa-chart-pie"></i> 分析概览</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-label">分析时间</span>
                    <span class="summary-value">${new Date().toLocaleString('zh-CN')}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">文档长度</span>
                    <span class="summary-value">${requirementInput.value.length} 字符</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">分析状态</span>
                    <span class="summary-value success">完成</span>
                </div>
            </div>
        </div>
        
        <div class="result-details">
            <h3><i class="fas fa-list-ul"></i> 详细分析结果</h3>
            <div class="analysis-content">
                ${formatAnalysisResult(result)}
            </div>
        </div>
    `;
    
    resultContent.innerHTML = resultHTML;
    currentAnalysisId = result.id || Date.now();
}

// 格式化分析结果
function formatAnalysisResult(result) {
    if (typeof result === 'string') {
        return `<div class="result-text">${result}</div>`;
    }
    
    let html = '';
    
    // 如果有结构化数据，按类别显示
    if (result.requirements) {
        html += `
            <div class="result-section">
                <h4>功能需求</h4>
                <ul class="requirement-list">
                    ${result.requirements.map(req => `<li>${req}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    if (result.analysis) {
        html += `
            <div class="result-section">
                <h4>分析结果</h4>
                <div class="analysis-text">${result.analysis}</div>
            </div>
        `;
    }
    
    if (result.suggestions) {
        html += `
            <div class="result-section">
                <h4>改进建议</h4>
                <ul class="suggestion-list">
                    ${result.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    // 如果没有结构化数据，显示原始结果
    if (!html) {
        html = `<div class="result-text">${JSON.stringify(result, null, 2)}</div>`;
    }
    
    return html;
}

// 处理清空
function handleClear() {
    if (requirementInput.value.trim() && !confirm('确定要清空所有内容吗？')) {
        return;
    }
    
    requirementInput.value = '';
    updateWordCount();
    resultSection.style.display = 'none';
    clearDraftFromStorage();
    showToast('内容已清空', 'success');
}

// 处理粘贴
async function handlePaste() {
    try {
        const text = await navigator.clipboard.readText();
        if (text) {
            requirementInput.value = text;
            updateWordCount();
            saveDraftToStorage();
            showToast('内容已粘贴', 'success');
        }
    } catch (error) {
        showToast('粘贴失败，请手动粘贴', 'warning');
    }
}

// 处理保存
function handleSave() {
    const content = requirementInput.value.trim();
    if (!content) {
        showToast('没有内容可保存', 'warning');
        return;
    }
    
    saveDraftToStorage();
    showToast('草稿已保存', 'success');
}

// 处理导出
function handleExport() {
    if (!currentAnalysisId) {
        showToast('没有可导出的分析结果', 'warning');
        return;
    }
    
    const content = generateExportContent();
    downloadFile(content, `需求分析结果_${new Date().toISOString().split('T')[0]}.txt`);
    showToast('结果已导出', 'success');
}

// 生成导出内容
function generateExportContent() {
    const timestamp = new Date().toLocaleString('zh-CN');
    const originalContent = requirementInput.value;
    const resultText = resultContent.textContent || '无分析结果';
    
    return `需求文档分析结果
===================

分析时间: ${timestamp}
分析ID: ${currentAnalysisId}

原始需求文档:
${originalContent}

分析结果:
${resultText}

---
由需求文档分析系统生成`;
}

// 下载文件
function downloadFile(content, filename) {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 本地存储操作
function saveDraftToStorage() {
    localStorage.setItem('requirement_draft', requirementInput.value);
}

function loadDraftFromStorage() {
    const draft = localStorage.getItem('requirement_draft');
    if (draft) {
        requirementInput.value = draft;
    }
}

function clearDraftFromStorage() {
    localStorage.removeItem('requirement_draft');
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toastIcon = toast.querySelector('.toast-icon');
    const toastMessage = toast.querySelector('.toast-message');
    
    // 设置图标
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    toastIcon.className = `toast-icon ${icons[type] || icons.info}`;
    toastMessage.textContent = message;
    
    // 设置样式类
    toast.className = `toast ${type}`;
    
    // 显示提示
    toast.classList.add('show');
    
    // 3秒后自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 应用防抖到输入事件
const debouncedSaveDraft = debounce(saveDraftToStorage, 1000);
requirementInput.addEventListener('input', debouncedSaveDraft);
