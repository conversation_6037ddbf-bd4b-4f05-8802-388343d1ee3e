const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'text/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

// 获取文件的MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'text/plain';
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 解析URL
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;
    
    // 如果请求根路径，重定向到index.html
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    // 构建文件路径
    const filePath = path.join(__dirname, pathname);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在，返回404
            res.writeHead(404, {
                'Content-Type': 'text/html; charset=utf-8',
                'Access-Control-Allow-Origin': '*'
            });
            res.end(`
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <title>404 - 页面未找到</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px;
                            background: #f8fafc;
                            color: #1e293b;
                        }
                        .error-container {
                            max-width: 500px;
                            margin: 0 auto;
                            background: white;
                            padding: 2rem;
                            border-radius: 8px;
                            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                        }
                        h1 { color: #ef4444; margin-bottom: 1rem; }
                        p { margin-bottom: 1.5rem; }
                        a { 
                            color: #2563eb; 
                            text-decoration: none;
                            padding: 0.5rem 1rem;
                            background: #eff6ff;
                            border-radius: 4px;
                            display: inline-block;
                        }
                        a:hover { background: #dbeafe; }
                    </style>
                </head>
                <body>
                    <div class="error-container">
                        <h1>404 - 页面未找到</h1>
                        <p>抱歉，您请求的页面不存在。</p>
                        <a href="/">返回首页</a>
                    </div>
                </body>
                </html>
            `);
            return;
        }
        
        // 读取文件
        fs.readFile(filePath, (err, data) => {
            if (err) {
                // 读取文件出错，返回500
                res.writeHead(500, {
                    'Content-Type': 'text/html; charset=utf-8',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end(`
                    <!DOCTYPE html>
                    <html lang="zh-CN">
                    <head>
                        <meta charset="UTF-8">
                        <title>500 - 服务器错误</title>
                        <style>
                            body { 
                                font-family: Arial, sans-serif; 
                                text-align: center; 
                                padding: 50px;
                                background: #f8fafc;
                                color: #1e293b;
                            }
                            .error-container {
                                max-width: 500px;
                                margin: 0 auto;
                                background: white;
                                padding: 2rem;
                                border-radius: 8px;
                                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                            }
                            h1 { color: #ef4444; margin-bottom: 1rem; }
                            p { margin-bottom: 1.5rem; }
                            a { 
                                color: #2563eb; 
                                text-decoration: none;
                                padding: 0.5rem 1rem;
                                background: #eff6ff;
                                border-radius: 4px;
                                display: inline-block;
                            }
                            a:hover { background: #dbeafe; }
                        </style>
                    </head>
                    <body>
                        <div class="error-container">
                            <h1>500 - 服务器错误</h1>
                            <p>服务器在处理您的请求时发生错误。</p>
                            <a href="/">返回首页</a>
                        </div>
                    </body>
                    </html>
                `);
                return;
            }
            
            // 成功读取文件，返回内容
            const mimeType = getMimeType(filePath);
            res.writeHead(200, {
                'Content-Type': mimeType + '; charset=utf-8',
                'Access-Control-Allow-Origin': '*',
                'Cache-Control': 'no-cache'
            });
            res.end(data);
        });
    });
});

// 启动服务器
const PORT = 5000;
const HOST = 'localhost';

server.listen(PORT, HOST, () => {
    console.log('🚀 需求文档分析系统前端服务器已启动');
    console.log(`📍 服务地址: http://${HOST}:${PORT}`);
    console.log(`🔗 后端API地址: http://localhost:8000`);
    console.log('📝 请确保后端服务已在8000端口启动');
    console.log('⏹️  按 Ctrl+C 停止服务器');
    console.log('');
    console.log('🌟 功能特性:');
    console.log('   • 现代化的用户界面设计');
    console.log('   • 实时字数统计');
    console.log('   • 自动保存草稿');
    console.log('   • 分析结果导出');
    console.log('   • 响应式布局');
    console.log('');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n👋 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请检查是否有其他服务在运行`);
        console.error('💡 您可以尝试以下解决方案:');
        console.error('   1. 关闭占用端口的程序');
        console.error('   2. 修改 server.js 中的 PORT 变量使用其他端口');
    } else {
        console.error('❌ 服务器启动失败:', err.message);
    }
    process.exit(1);
});

module.exports = server;
